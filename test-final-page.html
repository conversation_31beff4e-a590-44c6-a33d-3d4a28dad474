<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试最终页面功能</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .metric-card {
            display: inline-block;
            background: linear-gradient(135deg, #B3CDE0, #A2D9CE);
            color: white;
            padding: 15px 20px;
            margin: 10px;
            border-radius: 10px;
            text-align: center;
            min-width: 120px;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .download-btn {
            background: linear-gradient(135deg, #F5A623, #ff8c42);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            font-size: 1rem;
        }
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(245, 166, 35, 0.4);
        }
        .tab-container {
            margin-top: 20px;
        }
        .tab-buttons {
            display: flex;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 20px;
        }
        .tab-btn {
            background: none;
            border: none;
            padding: 12px 20px;
            cursor: pointer;
            border-radius: 8px 8px 0 0;
            margin-right: 5px;
        }
        .tab-btn.active {
            background: #B3CDE0;
            color: white;
        }
        .tab-content {
            display: none;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>法弈 - 最终页面功能测试</h1>
        
        <!-- 数据概览测试 -->
        <div class="test-section">
            <h3>📊 数据概览展示</h3>
            <div class="metric-card">
                <div class="metric-value">78%</div>
                <div class="metric-label">证据完整度</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">1项</div>
                <div class="metric-label">关键缺失</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">78%</div>
                <div class="metric-label">预估胜率</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">中等偏强</div>
                <div class="metric-label">案件强度</div>
            </div>
        </div>

        <!-- 标签页测试 -->
        <div class="test-section">
            <h3>📋 详细报告标签页</h3>
            <div class="tab-container">
                <div class="tab-buttons">
                    <button class="tab-btn active" onclick="switchTab('evidence')">证据链分析</button>
                    <button class="tab-btn" onclick="switchTab('legal')">法律分析</button>
                    <button class="tab-btn" onclick="switchTab('court')">模拟推演</button>
                    <button class="tab-btn" onclick="switchTab('document')">起诉文书</button>
                </div>
                
                <div id="evidence" class="tab-content active">
                    <h4>证据链完整性分析</h4>
                    <p><strong>证据完整度：78%</strong> - 当前已收集到关键证据6项，仍缺失1项核心证据</p>
                    <ul>
                        <li>网络购物订单：证明买卖合同关系成立</li>
                        <li>在线支付凭证：证明已支付货款12,888元</li>
                        <li>故障现象录像：记录电脑质量问题</li>
                        <li>客服沟通记录：证明被告拒绝履行义务</li>
                    </ul>
                </div>
                
                <div id="legal" class="tab-content">
                    <h4>适用法律条文</h4>
                    <p><strong>《消费者权益保护法》第二十四条：</strong>经营者提供的商品不符合质量要求的，消费者可以要求退货...</p>
                    <p><strong>《民法典》第五百七十七条：</strong>当事人一方不履行合同义务的，应当承担违约责任...</p>
                </div>
                
                <div id="court" class="tab-content">
                    <h4>争议焦点分析</h4>
                    <p><strong>主要争议点：</strong></p>
                    <ol>
                        <li>产品质量问题的认定</li>
                        <li>退货条件的满足</li>
                        <li>损失赔偿的合理性</li>
                    </ol>
                    <p><strong>预估胜率：78%</strong></p>
                </div>
                
                <div id="document" class="tab-content">
                    <h4>民事起诉状</h4>
                    <p><strong>原告：</strong>张三，男，1995年10月20日出生...</p>
                    <p><strong>被告：</strong>未来科技电脑有限公司...</p>
                    <p><strong>诉讼请求：</strong></p>
                    <ol>
                        <li>判令被告退还购物款12,888元</li>
                        <li>判令被告赔偿误工损失2,000元</li>
                        <li>判令被告承担诉讼费用</li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- 下载功能测试 -->
        <div class="test-section">
            <h3>📥 下载功能测试</h3>
            <button class="download-btn" onclick="testDownloadReport()">📄 下载完整报告</button>
            <button class="download-btn" onclick="testDownloadDocument()">📋 下载起诉文书</button>
        </div>
    </div>

    <script>
        // 标签页切换功能
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有按钮的活动状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');
            
            // 激活对应的按钮
            event.target.classList.add('active');
        }

        // 测试下载报告功能
        function testDownloadReport() {
            const content = `法弈智能法律诉讼辅助系统 - 完整分析报告

案件概况：
- 案件类型：买卖合同纠纷
- 争议金额：12,888元
- 预估胜率：78%
- 证据完整度：78%

证据分析：
已有证据：网络购物订单、在线支付凭证、故障现象录像、客服沟通记录等6项
关键缺失：第三方质检报告1项

法律依据：
- 《中华人民共和国消费者权益保护法》第二十四条
- 《中华人民共和国民法典》第五百七十七条

策略建议：
1. 立即获取第三方检测报告
2. 固化证据材料
3. 完善损失证明

生成时间：${new Date().toLocaleString()}`;
            
            downloadFile(content, '法弈分析报告.txt');
        }

        // 测试下载文书功能
        function testDownloadDocument() {
            const content = `民事起诉状

原告：张三，男，1995年10月20日出生，汉族
被告：未来科技电脑有限公司

案由：买卖合同纠纷

诉讼请求：
一、判令被告立即退还原告购物款人民币12,888元；
二、判令被告赔偿原告误工损失人民币2,000元；
三、判令被告承担本案全部诉讼费用。

此致
XX市XX区人民法院

起诉人：张三
${new Date().toLocaleDateString()}`;
            
            downloadFile(content, '民事起诉状.txt');
        }

        // 文件下载功能
        function downloadFile(content, filename) {
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            alert(`文件 "${filename}" 下载成功！`);
        }
    </script>
</body>
</html>
