<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-btn:hover {
            background: #0056b3;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>法弈系统导航功能测试</h1>
        
        <h2>测试步骤：</h2>
        <ol>
            <li>点击"打开主页面"按钮</li>
            <li>在主页面中点击"立即体验"</li>
            <li>等待示例数据自动填入后，点击"立即分析"</li>
            <li>等待分析完成后，点击"查看证据链分析报告"</li>
            <li>在证据链页面，点击"查看法律分析和案例指引"</li>
            <li>在法律分析页面，测试"← 返回证据链分析"按钮</li>
            <li>继续测试其他页面的返回功能</li>
        </ol>

        <h2>测试控制：</h2>
        <button class="test-btn" onclick="openMainPage()">打开主页面</button>
        <button class="test-btn" onclick="testNavigation()">自动测试导航</button>
        <button class="test-btn" onclick="checkElements()">检查页面元素</button>

        <div id="status" class="status">
            准备开始测试...
        </div>

        <h2>预期结果：</h2>
        <ul>
            <li>✅ 法律分析与案例指引页面应该有"← 返回证据链分析"按钮</li>
            <li>✅ 模拟法庭推演页面应该有"← 返回法律分析与案例指引"按钮</li>
            <li>✅ 起诉文书初稿页面应该有"← 返回模拟法庭推演"按钮</li>
            <li>✅ 点击返回按钮应该能正确跳转到上一个页面</li>
            <li>✅ 返回到证据链分析页面时应该显示之前的分析结果</li>
        </ul>
    </div>

    <script>
        function updateStatus(message, type = 'normal') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status';
            if (type === 'success') {
                status.classList.add('success');
            } else if (type === 'error') {
                status.classList.add('error');
            }
        }

        function openMainPage() {
            updateStatus('正在打开主页面...', 'normal');
            window.open('http://localhost:8000', '_blank');
            updateStatus('主页面已在新标签页中打开，请按照测试步骤进行操作', 'success');
        }

        function testNavigation() {
            updateStatus('自动测试功能暂未实现，请手动按照测试步骤操作', 'normal');
        }

        function checkElements() {
            updateStatus('正在检查页面元素...', 'normal');
            
            // 这里可以添加检查主页面元素的逻辑
            fetch('http://localhost:8000')
                .then(response => response.text())
                .then(html => {
                    const checks = [
                        { name: '返回证据链分析按钮', pattern: /backToEvidenceBtn/ },
                        { name: '返回法律分析按钮', pattern: /backToLegalInsightsBtn/ },
                        { name: '返回模拟法庭按钮', pattern: /backToCourtSimulationBtn/ },
                        { name: '返回按钮样式', pattern: /\.back-btn/ },
                        { name: '返回函数', pattern: /function backTo/ }
                    ];
                    
                    let results = [];
                    checks.forEach(check => {
                        if (check.pattern.test(html)) {
                            results.push(`✅ ${check.name}: 存在`);
                        } else {
                            results.push(`❌ ${check.name}: 缺失`);
                        }
                    });
                    
                    updateStatus('检查完成:\n' + results.join('\n'), 'success');
                })
                .catch(error => {
                    updateStatus('检查失败: ' + error.message, 'error');
                });
        }

        // 页面加载完成后自动检查
        window.addEventListener('load', function() {
            updateStatus('测试页面已加载，可以开始测试', 'success');
        });
    </script>
</body>
</html>
